import { BrowserWindow } from 'electron';
import path from 'node:path';
import { createLogger } from '../../src/libs/logger';

const logger = createLogger('WindowManager');

class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private preloadPath: string;
  private viteDevServerUrl: string | undefined;
  private webProdUrl = 'http://61.188.177.180:8899/';
  private isUserLoggedIn: boolean = false;

  constructor(preloadPath: string) {
    this.preloadPath = preloadPath;
    this.viteDevServerUrl = process.env['VITE_DEV_SERVER_URL'];
  }

  createMainWindow(): BrowserWindow {
    this.mainWindow = new BrowserWindow({
      show: false,
      webPreferences: {
        preload: this.preloadPath,
        webSecurity: false,
        nodeIntegration: true,
      },
      frame: false,
      width: 1000,
      height: 562.5,
      minWidth: 1000,
      minHeight: 562.5,
      backgroundColor: '#eee',
    });

    if (this.viteDevServerUrl) {
      this.mainWindow.loadURL(this.viteDevServerUrl);
    } else {
      this.mainWindow.loadURL(this.webProdUrl);
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on('maximize', () => {
      this.mainWindow?.webContents.send('window-state-changed', this.mainWindow.isMaximized());
    });

    this.mainWindow.on('unmaximize', () => {
      this.mainWindow?.webContents.send('window-state-changed', this.mainWindow.isMaximized());
    });

    logger.info('主窗口创建完成');
    return this.mainWindow;
  }

  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  minimizeMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.minimize();
    }
  }

  closeMainWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.session.clearStorageData({
        storages: ['localstorage'],
      });
      this.mainWindow.close();
    }
  }

  maximizeMainWindow(maximize?: boolean): void {
    if (!this.mainWindow) return;

    if (maximize !== undefined) {
      if (maximize) {
        this.mainWindow.maximize();
      } else {
        this.mainWindow.unmaximize();
      }
      return;
    }

    if (this.mainWindow.isMaximized()) {
      this.mainWindow.unmaximize();
    } else {
      this.mainWindow.maximize();
    }
  }

  // 登录状态管理
  setUserLoggedIn(isLoggedIn: boolean): void {
    this.isUserLoggedIn = isLoggedIn;
  }

  isLoggedIn(): boolean {
    return this.isUserLoggedIn;
  }
}

export default WindowManager;
