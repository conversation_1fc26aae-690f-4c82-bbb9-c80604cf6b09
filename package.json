{"name": "vite-project", "version": "0.0.0", "private": true, "main": "dist-electron/main.js", "scripts": {"dev": "vite --config vite/web.config.ts", "build": "run-p type-check && vite --config vite/web.config.ts build", "dev:electron": "vite --config vite/electron.config.ts", "build:electron": "run-p type-check && vite --config vite/electron.config.ts build && electron-builder", "preview": "vite --config vite/web.config.ts preview", "test": "vitest --config vite/vitest.config.ts", "prepare": "husky install", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.10.0", "element-plus": "^2.10.4", "md5": "^2.3.0", "pinia": "^3.0.3", "qs": "^6.14.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/mdi": "^1.2.3", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/md5": "^2.3.5", "@types/node": "^24.0.14", "@types/qs": "^6.14.0", "@unocss/preset-rem-to-px": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "electron": "^37.2.2", "electron-builder": "^26.0.12", "eslint": "^9.31.0", "eslint-plugin-oxlint": "^1.6.0", "eslint-plugin-vue": "~10.3.0", "husky": "^9.1.7", "jiti": "^2.4.2", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "npm-run-all2": "^8.0.4", "oxlint": "^1.6.0", "prettier": "3.6.2", "typescript": "~5.8.3", "unocss": "^66.3.3", "vite": "^7.0.4", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vite-plugin-mock-dev-server": "^1.9.1", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^3.0.1"}, "lint-staged": {"*.{ts,vue}": ["npm run lint:oxlint", "npm run format"]}}