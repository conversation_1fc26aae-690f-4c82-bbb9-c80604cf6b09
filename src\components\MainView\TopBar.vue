<script setup lang="ts">
import { shallowRef } from 'vue';
import router from '@/router';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/stores';
import UserDialog from '../UserDialog.vue';
import ConfigDialog from './ConfigDialog.vue';
import { Misc } from '@/script';

const store = useUserStore();
const { userInfo } = storeToRefs(store);
const { clearUserInfo } = store;
const userVisible = shallowRef(false);
const configVisible = shallowRef(false);

const handleCommand = (command: string) => {
  if (command === 'logout') {
    clearUserInfo();
    router.push({ name: 'login' });
  } else if (command === 'edit') {
    userVisible.value = true;
  } else if (command === 'config') {
    configVisible.value = true;
  }
};
</script>

<template>
  <div px-10 h-40 flex aic jcsb bg="[--g-panel-bg]">
    <div c-white fs-18>稳步前行</div>
    <div flex aic gap-10>
      <span c-white>{{ userInfo.user_name }}</span>
      <el-dropdown @command="handleCommand">
        <i mr-10 fs-26 i-mdi-account-circle cursor-pointer />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="edit">
              <i i-mdi-pencil></i>
              修改密码
            </el-dropdown-item>
            <el-dropdown-item v-if="Misc.isAdmin()" command="config">
              <i i-mdi-cog></i>
              修改配置
            </el-dropdown-item>
            <el-dropdown-item command="logout">
              <i i-mdi-logout></i>
              退出
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <UserDialog v-model="userVisible" :user="userInfo" />
    <ConfigDialog v-model="configVisible" />
  </div>
</template>

<style scoped></style>
